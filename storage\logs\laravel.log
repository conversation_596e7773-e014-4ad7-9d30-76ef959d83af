[2025-07-17 19:59:13] production.ERROR: Invalid API Key provided: sk_test_***********XXXX {"userId":2,"exception":"[object] (Stripe\\Exception\\AuthenticationException(code: 0): Invalid API Key provided: sk_test_***********XXXX at C:\\xampp\\htdocs\\zaptra\\vendor\\stripe\\stripe-php\\lib\\Exception\\ApiErrorException.php:38)
[stacktrace]
#0 C:\\xampp\\htdocs\\zaptra\\vendor\\stripe\\stripe-php\\lib\\ApiRequestor.php(216): Stripe\\Exception\\ApiErrorException::factory('Invalid API Key...', 401, '{\\n  \"error\": {\\n...', Array, Object(Stripe\\Util\\CaseInsensitiveArray), NULL)
#1 C:\\xampp\\htdocs\\zaptra\\vendor\\stripe\\stripe-php\\lib\\ApiRequestor.php(175): Stripe\\ApiRequestor::_specificAPIError('{\\n  \"error\": {\\n...', 401, Object(Stripe\\Util\\CaseInsensitiveArray), Array, Array)
#2 C:\\xampp\\htdocs\\zaptra\\vendor\\stripe\\stripe-php\\lib\\ApiRequestor.php(558): Stripe\\ApiRequestor->handleErrorResponse('{\\n  \"error\": {\\n...', 401, Object(Stripe\\Util\\CaseInsensitiveArray), Array)
#3 C:\\xampp\\htdocs\\zaptra\\vendor\\stripe\\stripe-php\\lib\\ApiRequestor.php(124): Stripe\\ApiRequestor->_interpretResponse('{\\n  \"error\": {\\n...', 401, Object(Stripe\\Util\\CaseInsensitiveArray))
#4 C:\\xampp\\htdocs\\zaptra\\vendor\\stripe\\stripe-php\\lib\\BaseStripeClient.php(145): Stripe\\ApiRequestor->request('post', '/v1/setup_inten...', Array, Array)
#5 C:\\xampp\\htdocs\\zaptra\\vendor\\stripe\\stripe-php\\lib\\Service\\AbstractService.php(75): Stripe\\BaseStripeClient->request('post', '/v1/setup_inten...', Array, Object(Stripe\\Util\\RequestOptions))
#6 C:\\xampp\\htdocs\\zaptra\\vendor\\stripe\\stripe-php\\lib\\Service\\SetupIntentService.php(87): Stripe\\Service\\AbstractService->request('post', '/v1/setup_inten...', Array, NULL)
#7 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\cashier\\src\\Concerns\\ManagesPaymentMethods.php(26): Stripe\\Service\\SetupIntentService->create(Array)
#8 C:\\xampp\\htdocs\\zaptra\\app\\Http\\Controllers\\PlansController.php(56): App\\Models\\User->createSetupIntent()
#9 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\PlansController->current()
#10 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('current', Array)
#11 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\PlansController), 'current')
#12 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#13 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#14 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\xampp\\htdocs\\zaptra\\app\\Http\\Middleware\\Activation.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\Activation->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\xampp\\htdocs\\zaptra\\app\\Http\\Middleware\\Impersonate.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\Impersonate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\xampp\\htdocs\\zaptra\\app\\Http\\Middleware\\Language.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\Language->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#31 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\zaptra\\vendor\\dacoto\\laravel-wizard-installer\\src\\Middleware\\ToInstallMiddleware.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): dacoto\\LaravelWizardInstaller\\Middleware\\ToInstallMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#40 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#42 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#44 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\zaptra\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#63 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#64 C:\\xampp\\htdocs\\zaptra\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#65 {main}
"} 
[2025-07-17 20:00:53] production.INFO: Facebook upload session request {"url":"https://graph.facebook.com/v19.0/585273477636023/uploads","file_length":499498,"file_type":"image/png","file_name":"zaptra-logo.jpg"} 
[2025-07-17 20:00:54] production.INFO: Facebook upload session response {"status":200,"body":"{\"id\":\"upload:MTphdHRhY2htZW50OjUyOWRhZWM2LTQ0MGUtNGYxNi1iODllLWNkY2NiZDk0NDEyOD9maWxlX2xlbmd0aD00OTk0OTgmZmlsZV90eXBlPWltYWdlJTJGcG5nJmZpbGVfbmFtZT16YXB0cmEtbG9nby5qcGc=?sig=ARbTc4oOsseJc9tTYIY\"}"} 
[2025-07-17 20:00:54] production.INFO: Facebook file upload request {"upload_url":"https://graph.facebook.com/v19.0/upload:MTphdHRhY2htZW50OjUyOWRhZWM2LTQ0MGUtNGYxNi1iODllLWNkY2NiZDk0NDEyOD9maWxlX2xlbmd0aD00OTk0OTgmZmlsZV90eXBlPWltYWdlJTJGcG5nJmZpbGVfbmFtZT16YXB0cmEtbG9nby5qcGc=?sig=ARbTc4oOsseJc9tTYIY","file_size":499498,"mime_type":"image/png"} 
[2025-07-17 20:00:55] production.INFO: Facebook file upload response {"status":200,"body":"{\"h\":\"4:emFwdHJhLWxvZ28uanBn:aW1hZ2UvcG5n:ARZUrmm1j7v2mVakju9Fog5GcWD5NC8b6AXfSQ4jfWcfp7-cVqM6HvyHWUTIpGgqA6SPEnWpPwpEs4T6DsCk9ZTQ77P4Z1MqWu8s_pZtOntL9A:e:1753108255:585273477636023:61577874446095:ARaPNThZu-twHMDlslc\"}"} 
[2025-07-17 20:00:55] production.INFO: Facebook upload successful {"handle":"4:emFwdHJhLWxvZ28uanBn:aW1hZ2UvcG5n:ARZUrmm1j7v2mVakju9Fog5GcWD5NC8b6AXfSQ4jfWcfp7-cVqM6HvyHWUTIpGgqA6SPEnWpPwpEs4T6DsCk9ZTQ77P4Z1MqWu8s_pZtOntL9A:e:1753108255:585273477636023:61577874446095:ARaPNThZu-twHMDlslc"} 
[2025-07-17 20:01:09] production.INFO: === PDF UPLOAD TEST START ===  
[2025-07-17 20:01:09] production.INFO: Starting PDF upload to Facebook  
[2025-07-17 20:01:09] production.INFO: PDF file details {"name":"AI_Overview_Sample.pdf","size":2023,"mime_type":"application/pdf","extension":"pdf"} 
[2025-07-17 20:01:09] production.INFO: Access token length: 204  
[2025-07-17 20:01:09] production.INFO: Facebook upload session request {"url":"https://graph.facebook.com/v19.0/585273477636023/uploads","file_length":2023,"file_type":"application/pdf","file_name":"AI_Overview_Sample.pdf"} 
[2025-07-17 20:01:09] production.INFO: Facebook upload session response {"status":200,"body":"{\"id\":\"upload:MTphdHRhY2htZW50OmQxZmFmODc5LWU0MGItNDEyZC1hMmViLTg2NTY3NmY1YTJkMz9maWxlX2xlbmd0aD0yMDIzJmZpbGVfdHlwZT1hcHBsaWNhdGlvbiUyRnBkZiZmaWxlX25hbWU9QUlfT3ZlcnZpZXdfU2FtcGxlLnBkZg==?sig=ARY4nLpQXwQxzPumHz8\"}"} 
[2025-07-17 20:01:09] production.INFO: Facebook file upload request {"upload_url":"https://graph.facebook.com/v19.0/upload:MTphdHRhY2htZW50OmQxZmFmODc5LWU0MGItNDEyZC1hMmViLTg2NTY3NmY1YTJkMz9maWxlX2xlbmd0aD0yMDIzJmZpbGVfdHlwZT1hcHBsaWNhdGlvbiUyRnBkZiZmaWxlX25hbWU9QUlfT3ZlcnZpZXdfU2FtcGxlLnBkZg==?sig=ARY4nLpQXwQxzPumHz8","file_size":2023,"mime_type":"application/pdf"} 
[2025-07-17 20:01:10] production.INFO: Facebook file upload response {"status":200,"body":"{\"h\":\"4:QUlfT3ZlcnZpZXdfU2FtcGxlLnBkZg==:YXBwbGljYXRpb24vcGRm:ARY1MpvnviMmOg6uvo9eznNYyXqxmf6icnkXGRfNEDBU66O5SRMIk0-ZLCGrp2ZyrnLVnxdOLhIUgjc06VUXYSn_6_daPMoGsfwv6K-PUUzwhA:e:1753108270:585273477636023:61577874446095:ARYKTKQCjMYh9lHjqLI\"}"} 
[2025-07-17 20:01:10] production.INFO: Facebook upload successful {"handle":"4:QUlfT3ZlcnZpZXdfU2FtcGxlLnBkZg==:YXBwbGljYXRpb24vcGRm:ARY1MpvnviMmOg6uvo9eznNYyXqxmf6icnkXGRfNEDBU66O5SRMIk0-ZLCGrp2ZyrnLVnxdOLhIUgjc06VUXYSn_6_daPMoGsfwv6K-PUUzwhA:e:1753108270:585273477636023:61577874446095:ARYKTKQCjMYh9lHjqLI"} 
[2025-07-17 20:01:10] production.INFO: Facebook upload result: 4:QUlfT3ZlcnZpZXdfU2FtcGxlLnBkZg==:YXBwbGljYXRpb24vcGRm:ARY1MpvnviMmOg6uvo9eznNYyXqxmf6icnkXGRfNEDBU66O5SRMIk0-ZLCGrp2ZyrnLVnxdOLhIUgjc06VUXYSn_6_daPMoGsfwv6K-PUUzwhA:e:1753108270:585273477636023:61577874446095:ARYKTKQCjMYh9lHjqLI  
[2025-07-17 20:01:10] production.INFO: === PDF UPLOAD TEST SUCCESS ===  
[2025-07-17 20:06:21] production.INFO: === PDF UPLOAD TEST START ===  
[2025-07-17 20:06:21] production.INFO: Starting PDF upload to Facebook  
[2025-07-17 20:06:21] production.INFO: PDF file details {"name":"AI_Overview_Sample.pdf","size":2023,"mime_type":"application/pdf","extension":"pdf"} 
[2025-07-17 20:06:21] production.INFO: Access token length: 204  
[2025-07-17 20:06:21] production.INFO: Facebook upload session request {"url":"https://graph.facebook.com/v19.0/585273477636023/uploads","file_length":2023,"file_type":"application/pdf","file_name":"AI_Overview_Sample.pdf"} 
[2025-07-17 20:06:22] production.INFO: Facebook upload session response {"status":200,"body":"{\"id\":\"upload:MTphdHRhY2htZW50OmQ5NjE1ZWU3LTAxODAtNDUyNi05NDkwLWY2MjhmNWI2ODJlOT9maWxlX2xlbmd0aD0yMDIzJmZpbGVfdHlwZT1hcHBsaWNhdGlvbiUyRnBkZiZmaWxlX25hbWU9QUlfT3ZlcnZpZXdfU2FtcGxlLnBkZg==?sig=ARaOPyTcZzWBvI7gJoQ\"}"} 
[2025-07-17 20:06:22] production.INFO: Facebook file upload request {"upload_url":"https://graph.facebook.com/v19.0/upload:MTphdHRhY2htZW50OmQ5NjE1ZWU3LTAxODAtNDUyNi05NDkwLWY2MjhmNWI2ODJlOT9maWxlX2xlbmd0aD0yMDIzJmZpbGVfdHlwZT1hcHBsaWNhdGlvbiUyRnBkZiZmaWxlX25hbWU9QUlfT3ZlcnZpZXdfU2FtcGxlLnBkZg==?sig=ARaOPyTcZzWBvI7gJoQ","file_size":2023,"mime_type":"application/pdf"} 
[2025-07-17 20:06:22] production.INFO: Facebook file upload response {"status":200,"body":"{\"h\":\"4:QUlfT3ZlcnZpZXdfU2FtcGxlLnBkZg==:YXBwbGljYXRpb24vcGRm:ARYMbqPDTBZKklwTjqh3E0hQShCm_OK7Tl5xN6HE8wdWDP5puquQV22o8qnhJPkJZZhV4wR0EJCH-STl75HG-FgO2SMFVGcGOzonqAFjwVaj0g:e:1753108582:585273477636023:61577874446095:ARZTjLbkvQ-pLxHq_qI\"}"} 
[2025-07-17 20:06:22] production.INFO: Facebook upload successful {"handle":"4:QUlfT3ZlcnZpZXdfU2FtcGxlLnBkZg==:YXBwbGljYXRpb24vcGRm:ARYMbqPDTBZKklwTjqh3E0hQShCm_OK7Tl5xN6HE8wdWDP5puquQV22o8qnhJPkJZZhV4wR0EJCH-STl75HG-FgO2SMFVGcGOzonqAFjwVaj0g:e:1753108582:585273477636023:61577874446095:ARZTjLbkvQ-pLxHq_qI"} 
[2025-07-17 20:06:22] production.INFO: Facebook upload result: 4:QUlfT3ZlcnZpZXdfU2FtcGxlLnBkZg==:YXBwbGljYXRpb24vcGRm:ARYMbqPDTBZKklwTjqh3E0hQShCm_OK7Tl5xN6HE8wdWDP5puquQV22o8qnhJPkJZZhV4wR0EJCH-STl75HG-FgO2SMFVGcGOzonqAFjwVaj0g:e:1753108582:585273477636023:61577874446095:ARZTjLbkvQ-pLxHq_qI  
[2025-07-17 20:06:22] production.INFO: === PDF UPLOAD TEST SUCCESS ===  
[2025-07-17 20:07:07] production.INFO: === TEMPLATE SUBMISSION START ===  
[2025-07-17 20:07:07] production.INFO: Template submission data {"name":"alviongspdf","category":"MARKETING","language":"en_US","allow_category_change":"1","components":[{"type":"BODY","text":"Hi {{1}},

Are you interested in evolving into ai please contact the below number for more information.","example":{"body_text":[["uma"]]}},{"type":"HEADER","format":"DOCUMENT","example":{"header_handle":["4:QUlfT3ZlcnZpZXdfU2FtcGxlLnBkZg==:YXBwbGljYXRpb24vcGRm:ARYMbqPDTBZKklwTjqh3E0hQShCm_OK7Tl5xN6HE8wdWDP5puquQV22o8qnhJPkJZZhV4wR0EJCH-STl75HG-FgO2SMFVGcGOzonqAFjwVaj0g:e:1753108582:585273477636023:61577874446095:ARZTjLbkvQ-pLxHq_qI"],"header_url":["http://localhost/zaptra/public/uploads/media/bnKuhPYsniDreFisY2jvgh75Y8fQcrx7fqgG6kdo.pdf"]}},{"type":"BUTTONS","buttons":[{"type":"PHONE_NUMBER","text":"Call now","phone_number":"919346015886"}]}]} 
[2025-07-17 20:07:07] production.INFO: === FACEBOOK TEMPLATE SUBMISSION ===  
[2025-07-17 20:07:07] production.INFO: Template submission URL: https://graph.facebook.com/v19.0/754212127282280/message_templates  
[2025-07-17 20:07:07] production.INFO: Access token length: 204  
[2025-07-17 20:07:07] production.INFO: Template data being sent: {"name":"alviongspdf","category":"MARKETING","language":"en_US","allow_category_change":"1","components":[{"type":"BODY","text":"Hi {{1}},

Are you interested in evolving into ai please contact the below number for more information.","example":{"body_text":[["uma"]]}},{"type":"HEADER","format":"DOCUMENT","example":{"header_handle":["4:QUlfT3ZlcnZpZXdfU2FtcGxlLnBkZg==:YXBwbGljYXRpb24vcGRm:ARYMbqPDTBZKklwTjqh3E0hQShCm_OK7Tl5xN6HE8wdWDP5puquQV22o8qnhJPkJZZhV4wR0EJCH-STl75HG-FgO2SMFVGcGOzonqAFjwVaj0g:e:1753108582:585273477636023:61577874446095:ARZTjLbkvQ-pLxHq_qI"],"header_url":["http://localhost/zaptra/public/uploads/media/bnKuhPYsniDreFisY2jvgh75Y8fQcrx7fqgG6kdo.pdf"]}},{"type":"BUTTONS","buttons":[{"type":"PHONE_NUMBER","text":"Call now","phone_number":"919346015886"}]}]} 
[2025-07-17 20:07:08] production.INFO: Facebook API response status: 500  
[2025-07-17 20:07:08] production.INFO: Facebook API response body: {"error":{"message":"An unknown error has occurred.","type":"OAuthException","code":1,"fbtrace_id":"AnhRYbVqcqpD4a00hgSQWSg"}} 
[2025-07-17 20:07:08] production.INFO: Template submission result {"result":{"status":500,"content":{"error":{"message":"An unknown error has occurred.","type":"OAuthException","code":1,"fbtrace_id":"AnhRYbVqcqpD4a00hgSQWSg"}}}} 
[2025-07-17 20:07:08] production.ERROR: === TEMPLATE SUBMISSION FAILED ===  
[2025-07-17 20:07:08] production.ERROR: Template submission error {"result":{"status":500,"content":{"error":{"message":"An unknown error has occurred.","type":"OAuthException","code":1,"fbtrace_id":"AnhRYbVqcqpD4a00hgSQWSg"}}}} 
[2025-07-17 20:09:52] production.INFO: === PDF UPLOAD TEST START ===  
[2025-07-17 20:09:52] production.INFO: Starting PDF upload to Facebook  
[2025-07-17 20:09:52] production.INFO: PDF file details {"name":"AI_Overview_Sample.pdf","size":2023,"mime_type":"application/pdf","extension":"pdf"} 
[2025-07-17 20:09:52] production.INFO: Access token length: 204  
[2025-07-17 20:09:52] production.INFO: Facebook upload session request {"url":"https://graph.facebook.com/v19.0/585273477636023/uploads","file_length":2023,"file_type":"application/pdf","file_name":"AI_Overview_Sample.pdf"} 
[2025-07-17 20:09:53] production.INFO: Facebook upload session response {"status":200,"body":"{\"id\":\"upload:MTphdHRhY2htZW50OjgwNzk2ZTNhLTVlMmYtNGE4Zi1hMzBiLWYyMzA5OGNkY2Q1Zj9maWxlX2xlbmd0aD0yMDIzJmZpbGVfdHlwZT1hcHBsaWNhdGlvbiUyRnBkZiZmaWxlX25hbWU9QUlfT3ZlcnZpZXdfU2FtcGxlLnBkZg==?sig=ARaIQ9e55FPAK16-bh0\"}"} 
[2025-07-17 20:09:53] production.INFO: Facebook file upload request {"upload_url":"https://graph.facebook.com/v19.0/upload:MTphdHRhY2htZW50OjgwNzk2ZTNhLTVlMmYtNGE4Zi1hMzBiLWYyMzA5OGNkY2Q1Zj9maWxlX2xlbmd0aD0yMDIzJmZpbGVfdHlwZT1hcHBsaWNhdGlvbiUyRnBkZiZmaWxlX25hbWU9QUlfT3ZlcnZpZXdfU2FtcGxlLnBkZg==?sig=ARaIQ9e55FPAK16-bh0","file_size":2023,"mime_type":"application/pdf"} 
[2025-07-17 20:09:54] production.INFO: Facebook file upload response {"status":200,"body":"{\"h\":\"4:QUlfT3ZlcnZpZXdfU2FtcGxlLnBkZg==:YXBwbGljYXRpb24vcGRm:ARbJ1klvMx-IK32PWXb7WYP9_8NU6acjcg9PazElranOPHyoV-WsC8oNZZBMrJrJfNZsCeSnnfmrxik3u1D1Wsu5041FkZ3L19qtH0aZlMtqEw:e:1753108794:585273477636023:61577874446095:ARYkRPDMRhwsxu8nW-M\"}"} 
[2025-07-17 20:09:54] production.INFO: Facebook upload successful {"handle":"4:QUlfT3ZlcnZpZXdfU2FtcGxlLnBkZg==:YXBwbGljYXRpb24vcGRm:ARbJ1klvMx-IK32PWXb7WYP9_8NU6acjcg9PazElranOPHyoV-WsC8oNZZBMrJrJfNZsCeSnnfmrxik3u1D1Wsu5041FkZ3L19qtH0aZlMtqEw:e:1753108794:585273477636023:61577874446095:ARYkRPDMRhwsxu8nW-M"} 
[2025-07-17 20:09:54] production.INFO: Facebook upload result: 4:QUlfT3ZlcnZpZXdfU2FtcGxlLnBkZg==:YXBwbGljYXRpb24vcGRm:ARbJ1klvMx-IK32PWXb7WYP9_8NU6acjcg9PazElranOPHyoV-WsC8oNZZBMrJrJfNZsCeSnnfmrxik3u1D1Wsu5041FkZ3L19qtH0aZlMtqEw:e:1753108794:585273477636023:61577874446095:ARYkRPDMRhwsxu8nW-M  
[2025-07-17 20:09:54] production.INFO: === PDF UPLOAD TEST SUCCESS ===  
[2025-07-17 20:10:30] production.INFO: === TEMPLATE SUBMISSION START ===  
[2025-07-17 20:10:30] production.INFO: Template submission data {"name":"alviongspdf","category":"MARKETING","language":"en_US","allow_category_change":"1","components":[{"type":"BODY","text":"Hi {{1}},

Are you interested in evolving into ai please contact the below number for more information.","example":{"body_text":[["uma"]]}},{"type":"HEADER","format":"DOCUMENT","example":{"header_handle":["4:QUlfT3ZlcnZpZXdfU2FtcGxlLnBkZg==:YXBwbGljYXRpb24vcGRm:ARbJ1klvMx-IK32PWXb7WYP9_8NU6acjcg9PazElranOPHyoV-WsC8oNZZBMrJrJfNZsCeSnnfmrxik3u1D1Wsu5041FkZ3L19qtH0aZlMtqEw:e:1753108794:585273477636023:61577874446095:ARYkRPDMRhwsxu8nW-M"]}},{"type":"BUTTONS","buttons":[{"type":"PHONE_NUMBER","text":"Call now","phone_number":"919346015886"}]}]} 
[2025-07-17 20:10:30] production.INFO: === FACEBOOK TEMPLATE SUBMISSION ===  
[2025-07-17 20:10:30] production.INFO: Template submission URL: https://graph.facebook.com/v19.0/754212127282280/message_templates  
[2025-07-17 20:10:30] production.INFO: Access token length: 204  
[2025-07-17 20:10:30] production.INFO: Template data being sent: {"name":"alviongspdf","category":"MARKETING","language":"en_US","allow_category_change":"1","components":[{"type":"BODY","text":"Hi {{1}},

Are you interested in evolving into ai please contact the below number for more information.","example":{"body_text":[["uma"]]}},{"type":"HEADER","format":"DOCUMENT","example":{"header_handle":["4:QUlfT3ZlcnZpZXdfU2FtcGxlLnBkZg==:YXBwbGljYXRpb24vcGRm:ARbJ1klvMx-IK32PWXb7WYP9_8NU6acjcg9PazElranOPHyoV-WsC8oNZZBMrJrJfNZsCeSnnfmrxik3u1D1Wsu5041FkZ3L19qtH0aZlMtqEw:e:1753108794:585273477636023:61577874446095:ARYkRPDMRhwsxu8nW-M"]}},{"type":"BUTTONS","buttons":[{"type":"PHONE_NUMBER","text":"Call now","phone_number":"919346015886"}]}]} 
[2025-07-17 20:10:40] production.INFO: Facebook API response status: 200  
[2025-07-17 20:10:40] production.INFO: Facebook API response body: {"id":"1851818335540827","status":"PENDING","category":"MARKETING"} 
[2025-07-17 20:10:40] production.INFO: Template submission result {"result":{"status":200,"content":{"id":"1851818335540827","status":"PENDING","category":"MARKETING"}}} 
[2025-07-17 20:10:40] production.INFO: === TEMPLATE SUBMISSION SUCCESS ===  
[2025-07-18 11:41:46] production.ERROR: Class "WpboxController" does not exist {"exception":"[object] (ReflectionException(code: -1): Class \"WpboxController\" does not exist at C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php:225)
[stacktrace]
#0 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(225): ReflectionClass->__construct('WpboxController')
#1 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(147): Illuminate\\Foundation\\Console\\RouteListCommand->isVendorRoute(Object(Illuminate\\Routing\\Route))
#2 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(116): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#3 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->{closure:Illuminate\\Foundation\\Console\\RouteListCommand::getRoutes():115}(Object(Illuminate\\Routing\\Route), 147)
#4 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(600): array_map(Object(Closure), Array, Array)
#5 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(777): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#6 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(115): Illuminate\\Support\\Collection->map(Object(Closure))
#7 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(101): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#8 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#9 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#10 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#14 C:\\xampp\\htdocs\\zaptra\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 C:\\xampp\\htdocs\\zaptra\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\xampp\\htdocs\\zaptra\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\xampp\\htdocs\\zaptra\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 C:\\xampp\\htdocs\\zaptra\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 {main}
"} 
[2025-07-18 14:34:02] production.ERROR: Attempt to read property "id" on null {"userId":1,"exception":"[object] (ErrorException(code: 0): Attempt to read property \"id\" on null at C:\\xampp\\htdocs\\zaptra\\modules\\Embedwhatsapp\\Http\\Controllers\\Main.php:79)
[stacktrace]
#0 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(255): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Attempt to read...', 'C:\\\\xampp\\\\htdocs...', 79)
#1 C:\\xampp\\htdocs\\zaptra\\modules\\Embedwhatsapp\\Http\\Controllers\\Main.php(79): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Attempt to read...', 'C:\\\\xampp\\\\htdocs...', 79)
#2 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Modules\\Embedwhatsapp\\Http\\Controllers\\Main->store(Object(Illuminate\\Http\\Request))
#3 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('store', Array)
#4 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Modules\\Embedwhatsapp\\Http\\Controllers\\Main), 'store')
#5 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#6 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#7 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\xampp\\htdocs\\zaptra\\app\\Http\\Middleware\\XssSanitization.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\XssSanitization->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 C:\\xampp\\htdocs\\zaptra\\app\\Http\\Middleware\\Language.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\Language->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#22 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp\\htdocs\\zaptra\\vendor\\dacoto\\laravel-wizard-installer\\src\\Middleware\\ToInstallMiddleware.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): dacoto\\LaravelWizardInstaller\\Middleware\\ToInstallMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#32 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#33 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#34 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\xampp\\htdocs\\zaptra\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#54 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#55 C:\\xampp\\htdocs\\zaptra\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#56 {main}
"} 
[2025-07-18 14:34:19] production.ERROR: Attempt to read property "id" on null {"userId":1,"exception":"[object] (ErrorException(code: 0): Attempt to read property \"id\" on null at C:\\xampp\\htdocs\\zaptra\\modules\\Embedwhatsapp\\Http\\Controllers\\Main.php:79)
[stacktrace]
#0 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(255): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Attempt to read...', 'C:\\\\xampp\\\\htdocs...', 79)
#1 C:\\xampp\\htdocs\\zaptra\\modules\\Embedwhatsapp\\Http\\Controllers\\Main.php(79): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Attempt to read...', 'C:\\\\xampp\\\\htdocs...', 79)
#2 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Modules\\Embedwhatsapp\\Http\\Controllers\\Main->store(Object(Illuminate\\Http\\Request))
#3 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('store', Array)
#4 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Modules\\Embedwhatsapp\\Http\\Controllers\\Main), 'store')
#5 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#6 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#7 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\xampp\\htdocs\\zaptra\\app\\Http\\Middleware\\XssSanitization.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\XssSanitization->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 C:\\xampp\\htdocs\\zaptra\\app\\Http\\Middleware\\Language.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\Language->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#22 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp\\htdocs\\zaptra\\vendor\\dacoto\\laravel-wizard-installer\\src\\Middleware\\ToInstallMiddleware.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): dacoto\\LaravelWizardInstaller\\Middleware\\ToInstallMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#32 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#33 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#34 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\xampp\\htdocs\\zaptra\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#54 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#55 C:\\xampp\\htdocs\\zaptra\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#56 {main}
"} 
[2025-07-18 14:34:23] production.ERROR: Attempt to read property "id" on null {"userId":1,"exception":"[object] (ErrorException(code: 0): Attempt to read property \"id\" on null at C:\\xampp\\htdocs\\zaptra\\modules\\Embedwhatsapp\\Http\\Controllers\\Main.php:79)
[stacktrace]
#0 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(255): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Attempt to read...', 'C:\\\\xampp\\\\htdocs...', 79)
#1 C:\\xampp\\htdocs\\zaptra\\modules\\Embedwhatsapp\\Http\\Controllers\\Main.php(79): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Attempt to read...', 'C:\\\\xampp\\\\htdocs...', 79)
#2 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Modules\\Embedwhatsapp\\Http\\Controllers\\Main->store(Object(Illuminate\\Http\\Request))
#3 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('store', Array)
#4 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Modules\\Embedwhatsapp\\Http\\Controllers\\Main), 'store')
#5 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#6 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#7 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\xampp\\htdocs\\zaptra\\app\\Http\\Middleware\\XssSanitization.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\XssSanitization->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 C:\\xampp\\htdocs\\zaptra\\app\\Http\\Middleware\\Language.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\Language->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#22 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp\\htdocs\\zaptra\\vendor\\dacoto\\laravel-wizard-installer\\src\\Middleware\\ToInstallMiddleware.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): dacoto\\LaravelWizardInstaller\\Middleware\\ToInstallMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#32 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#33 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#34 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\xampp\\htdocs\\zaptra\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#54 C:\\xampp\\htdocs\\zaptra\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#55 C:\\xampp\\htdocs\\zaptra\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#56 {main}
"} 
