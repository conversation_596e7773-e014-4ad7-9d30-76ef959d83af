import {
  ReactFlow,
  Background,
  useNodesState,
  useEdgesState,
  addEdge,
  Connection,
  Edge,
  Node,
} from '@xyflow/react';
import '@xyflow/react/dist/style.css';
import { Button } from "@/components/ui/button";
import { Save, ArrowLeft } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { useCallback, useState } from 'react';
import ActionPanel from './ActionPanel';
import { nodeTypes } from '@/config/nodeTypes';
import DataSidebar from './DataSidebar';

interface FlowCanvasProps {
  flowId?: string;
}

const flow_data = window.data?.flow?.flow_data || "{}";
console.log('🔍 Raw flow_data from window:', flow_data);
console.log('🔍 Type of flow_data:', typeof flow_data);

let parsedFlowData;
try {
  // Handle both string and object cases
  if (typeof flow_data === 'string') {
    parsedFlowData = JSON.parse(flow_data);
  } else {
    parsedFlowData = flow_data;
  }
} catch (e) {
  console.error('🚨 Error parsing flow_data:', e);
  parsedFlowData = {};
}

console.log('🔍 Parsed flow data:', parsedFlowData);

const initialNodes: Node[] = parsedFlowData?.nodes || [];
const initialEdges: Edge[] = parsedFlowData?.edges || [];

// Default edge options for animated black edges
const defaultEdgeOptions = {
  animated: true,
  style: {
    stroke: '#000000',
  },
};

console.log('🔍 Initial nodes:', initialNodes);
console.log('🔍 Initial edges:', initialEdges);

// Check for condition sharing in initial nodes
console.log('🔍 Checking for condition sharing in initial nodes...');
initialNodes.forEach((node, index) => {
  if (node.type === 'branch') {
    console.log(`🔍 Branch node ${index} (${node.id}):`, {
      conditions: node.data?.settings?.conditions,
      conditionIds: node.data?.settings?.conditions?.map(c => c.id),
      conditionReferences: node.data?.settings?.conditions?.map(c => c)
    });

    // Check if conditions are the same reference as other nodes
    if (index > 0) {
      const firstNodeConditions = initialNodes[0].data?.settings?.conditions;
      const currentNodeConditions = node.data?.settings?.conditions;
      console.log(`🔍 Node ${index} conditions same reference as node 0?`, firstNodeConditions === currentNodeConditions);
      if (firstNodeConditions && currentNodeConditions && firstNodeConditions.length > 0 && currentNodeConditions.length > 0) {
        console.log(`🔍 First condition same reference?`, firstNodeConditions[0] === currentNodeConditions[0]);
      }
    }
  }
});

// Fix the reference sharing issue at the data loading level
const fixedInitialNodes = initialNodes.map(node => {
  if (node.type === 'branch' && node.data?.settings?.conditions) {
    console.log(`🔧 Fixing conditions for node ${node.id}`);
    return {
      ...node,
      data: {
        ...node.data,
        settings: {
          ...node.data.settings,
          conditions: node.data.settings.conditions.map(condition => ({
            ...condition,
            id: `${Date.now()}-${Math.random().toString(36).substring(2, 15)}-${node.id.split('-')[1]}`
          }))
        }
      }
    };
  }
  return node;
});

console.log('🔧 Fixed initial nodes:', fixedInitialNodes);
console.log('🔧 Checking fixed nodes for condition sharing...');
fixedInitialNodes.forEach((node, index) => {
  if (node.type === 'branch') {
    console.log(`🔧 Fixed branch node ${index} (${node.id}):`, {
      conditions: node.data?.settings?.conditions,
      conditionIds: node.data?.settings?.conditions?.map(c => c.id)
    });
  }
});
console.log('🔍 ========= Window data ========');
console.log(window.data);


const FlowCanvas = ({ flowId = '1' }: FlowCanvasProps) => {
  const [nodes, setNodes, onNodesChange] = useNodesState(fixedInitialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);
  const [dataDrawerOpen, setDataDrawerOpen] = useState(false);
  const { toast } = useToast();

  const onConnect = useCallback(
    (params: Connection | Edge) => {
      const newEdge = {
        ...params,
        id: `e${Date.now()}`,
      };
      return setEdges((eds) => addEdge(newEdge, eds));
    },
    [setEdges],
  );

  const handleSave = async () => {
    const nodesWithData = nodes.map(node => ({
      ...node,
      data: {
        ...node.data,
        settings: node.data?.settings || {},
      },
    }));

    const flowData = {
      nodes: nodesWithData,
      edges,
    };
    
    console.log('Saving flow:', flowData);
    console.log('Flow ID:', window.data.flow.id);
    console.log('Base URL:', window.baseUrl);

    try {
      const saveUrl = `/flowmaker/update/${window.data.flow.id}`;
      console.log('Save URL:', saveUrl);

      const response = await fetch(saveUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
        },
        body: JSON.stringify(flowData),
      });

      console.log('Response status:', response.status);
      console.log('Response ok:', response.ok);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Response error:', errorText);
        throw new Error(`Failed to save flow: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      console.log('Save result:', result);

      toast({
        title: "Flow saved",
        description: "Your flow has been saved successfully.",
      });
    } catch (error) {
      console.error('Error saving flow:', error);
      toast({
        title: "Save failed",
        description: `Failed to save flow: ${error.message}`,
        variant: "destructive",
      });
    }
  };

  const handlePopoverIndexChange = (index: number | null) => {
    // No longer automatically opening the data sidebar when Data popover is shown
  };
  
  const handleOpenDataSidebar = () => {
    setDataDrawerOpen(true);
  };

  const handleBackClick = () => {
    window.location.href = "/flows";
  };

  return (
    <div className="flex h-screen bg-[#F1F0FB] relative">
      {/* Left Sidebar */}
      <div className="absolute left-0 top-0 m-2.5 z-10">
        <div className="w-20 flex flex-col items-center py-4 space-y-6">
          <div className="flex flex-col items-center space-y-6">
            <ActionPanel 
              onImportClick={() => {}}
              onPopoverIndexChange={handlePopoverIndexChange}
              onOpenDataSidebar={handleOpenDataSidebar}
            />
          </div>
        </div>
      </div>

      {/* Main Flow Area */}
      <div className="flex-1 relative">
        {/* Back and Save Buttons */}
        <div className="absolute top-4 right-4 z-10 flex space-x-2">
          <Button 
            onClick={handleBackClick}
            variant="outline"
            size="default"
            className="gap-2 bg-white"
          >
            <ArrowLeft className="h-4 w-4" />
            Back
          </Button>
          <Button 
            onClick={handleSave}
            variant="outline"
            size="default"
            className="gap-2 bg-white"
          >
            <Save className="h-4 w-4" />
            Save
          </Button>
        </div>

        <ReactFlow
          nodes={nodes}
          edges={edges}
          onNodesChange={onNodesChange}
          onEdgesChange={onEdgesChange}
          onConnect={onConnect}
          nodeTypes={nodeTypes}
          defaultEdgeOptions={defaultEdgeOptions}
          minZoom={0.1}
          maxZoom={4}
          fitView
        >
          <Background />
        </ReactFlow>

        {/* Data Sidebar */}
        <DataSidebar 
          open={dataDrawerOpen}
          onOpenChange={setDataDrawerOpen}
        />
      </div>
    </div>
  );
};

export default FlowCanvas;
