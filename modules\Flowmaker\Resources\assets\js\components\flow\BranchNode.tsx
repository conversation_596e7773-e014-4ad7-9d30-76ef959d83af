import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Position, useReactFlow } from '@xyflow/react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { GitFork, Plus, X } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { BranchCondition, WebhookVariable, NodeData } from '@/types/flow';
import { useFlowVariables } from '@/hooks/useFlowVariables';
import { useFlowActions } from "@/hooks/useFlowActions";

interface BranchNodeProps {
  id: string;
  data: NodeData;
}



const operators = [
  { value: 'equals', label: 'Equals' },
  { value: 'not_equals', label: 'Not Equals' },
  { value: 'greater_than', label: 'Greater Than' },
  { value: 'less_than', label: 'Less Than' },
  { value: 'contains', label: 'Contains' },
  { value: 'not_contains', label: 'Not Contains' },
] as const;

const BranchNode = ({ id, data }: BranchNodeProps) => {
  console.log('🔍 BranchNode render - ID:', id, 'Full data:', data);
  console.log('🔍 BranchNode conditions for', id, ':', data.settings?.conditions);

  const { getNodes, setNodes } = useReactFlow();
  const { groupedVariables } = useFlowVariables();

  // Use local state for conditions to ensure complete isolation
  const [conditions, setConditions] = useState<BranchCondition[]>(() => {
    // Initialize with deep clone to prevent reference sharing
    const initialConditions = JSON.parse(JSON.stringify(data.settings?.conditions || []));
    console.log('🔍 Initial conditions for node', id, ':', initialConditions);
    return initialConditions;
  });

  // Sync local state with data changes, but only if data actually changed
  useEffect(() => {
    const dataConditions = data.settings?.conditions || [];
    const dataConditionsStr = JSON.stringify(dataConditions);
    const localConditionsStr = JSON.stringify(conditions);

    console.log('🔄 useEffect sync check for node', id, {
      dataConditions,
      localConditions: conditions,
      dataConditionsStr,
      localConditionsStr,
      areEqual: dataConditionsStr === localConditionsStr
    });

    if (dataConditionsStr !== localConditionsStr) {
      console.log('� Syncing conditions for node', id, 'from:', conditions, 'to:', dataConditions);
      setConditions(JSON.parse(JSON.stringify(dataConditions)));
    }
  }, [data.settings?.conditions, id]);

  // Update the global state whenever local conditions change
  useEffect(() => {
    console.log('🔄 Local conditions changed for node', id, ':', conditions);
    setNodes((nodes) =>
      nodes.map((node) => {
        if (node.id === id) {
          const updatedNode = {
            ...node,
            data: {
              ...node.data,
              settings: {
                ...node.data.settings,
                conditions: JSON.parse(JSON.stringify(conditions)), // Deep clone to prevent sharing
              },
            }
          };
          console.log('🔄 Updated global state for node', id, ':', updatedNode.data.settings.conditions);
          return updatedNode;
        }
        return node;
      })
    );
  }, [conditions, id, setNodes]);

  const webhookNode = getNodes().find((node) => node.type === 'webhook');
  const webhookVariables = (webhookNode?.data as NodeData)?.settings?.webhook?.variables || [];



  const addCondition = () => {
    // Generate a more unique ID using timestamp + random
    const uniqueId = `${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;
    console.log('🚀 Adding condition with ID:', uniqueId, 'to node:', id);
    console.log('🚀 Current conditions before add:', conditions);

    const newCondition: BranchCondition = {
      id: uniqueId,
      variableId: '',
      operator: 'equals',
      value: '',
    };
    const newConditions = [...conditions, newCondition];
    console.log('🚀 New conditions for node', id, ':', newConditions);

    // Update local state - this will trigger the useEffect to update global state
    setConditions(newConditions);
  };

  const removeCondition = (conditionId: string) => {
    const newConditions = conditions.filter((c) => c.id !== conditionId);
    console.log('🚀 Removing condition', conditionId, 'from node', id, 'new conditions:', newConditions);

    // Update local state - this will trigger the useEffect to update global state
    setConditions(newConditions);
  };

  return (
    <div className="bg-white rounded-lg shadow-lg">
      <Handle 
        type="target" 
        position={Position.Left}
        className="!bg-gray-300 !w-3 !h-3 !rounded-full"
      />
      
      <div className="flex items-center gap-2 mb-4 pb-2 border-b border-gray-100 px-4 pt-3 bg-gray-50">
        <GitFork className="h-4 w-4 text-purple-600" />
        <div className="font-medium">Branch</div>
      </div>

      <div className="p-4">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-sm font-medium">Branch</h3>
            <Button variant="outline" size="sm" onClick={addCondition}>
              <Plus className="h-4 w-4 mr-1" />
              Add Condition
            </Button>
          </div>

          <div className="space-y-3">
            {conditions.map((condition) => (
              <div key={condition.id} className="flex items-center gap-2">
                <Select
                  value={condition.variableId}
                  onValueChange={(value) => {
                    console.log('🚀 Updating variable for condition', condition.id, 'in node', id, 'to:', value);
                    const updatedConditions = conditions.map((c) =>
                      c.id === condition.id ? { ...c, variableId: value } : c
                    );
                    console.log('🚀 Updated conditions for node', id, ':', updatedConditions);

                    // Update local state - this will trigger the useEffect to update global state
                    setConditions(updatedConditions);
                  }}
                >
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Variable" />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.entries(groupedVariables).map(([category, categoryVariables]) => (
                      <div key={category}>
                        <div className="px-2 py-1.5 text-xs font-semibold text-gray-500 uppercase tracking-wider">
                          {category}
                        </div>
                        {categoryVariables.map((variable) => (
                          <SelectItem key={variable.value} value={variable.value}>
                            {variable.label}
                          </SelectItem>
                        ))}
                      </div>
                    ))}
                  </SelectContent>
                </Select>

                <Select
                  value={condition.operator}
                  onValueChange={(value: BranchCondition['operator']) => {
                    console.log('🚀 Updating operator for condition', condition.id, 'in node', id, 'to:', value);
                    const updatedConditions = conditions.map((c) =>
                      c.id === condition.id ? { ...c, operator: value } : c
                    );
                    console.log('🚀 Updated conditions for node', id, ':', updatedConditions);

                    // Update local state - this will trigger the useEffect to update global state
                    setConditions(updatedConditions);
                  }}
                >
                  <SelectTrigger className="w-[120px]">
                    <SelectValue placeholder="Operator" />
                  </SelectTrigger>
                  <SelectContent>
                    {operators.map((op) => (
                      <SelectItem key={op.value} value={op.value}>
                        {op.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Input
                  placeholder="Value"
                  value={condition.value}
                  onChange={(e) => {
                    console.log('🚀 Updating value for condition', condition.id, 'in node', id, 'to:', e.target.value);
                    const updatedConditions = conditions.map((c) =>
                      c.id === condition.id ? { ...c, value: e.target.value } : c
                    );
                    console.log('🚀 Updated conditions for node', id, ':', updatedConditions);

                    // Update local state - this will trigger the useEffect to update global state
                    setConditions(updatedConditions);
                  }}
                  className="flex-1"
                />

                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => removeCondition(condition.id)}
                >
                  <X className="h-4 w-4" />
                </Button>

                <div className="flex flex-col gap-4 ml-2">
                  <Handle
                    type="source"
                    position={Position.Right}
                    id={`condition-${condition.id}-true`}
                    className="!bg-green-500 !w-3 !h-3 !rounded-full"
                    style={{ top: '25%' }}
                  />
                  <Handle
                    type="source"
                    position={Position.Right}
                    id={`condition-${condition.id}-false`}
                    className="!bg-red-500 !w-3 !h-3 !rounded-full"
                    style={{ top: '75%' }}
                  />
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default BranchNode;